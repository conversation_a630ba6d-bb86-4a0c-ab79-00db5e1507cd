<?php

namespace App\Rules\Cart;

use App\Models\Product\Product;
use App\Models\Product\ProductVariation;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Product Variant Exists Rule
 *
 * Validates that a product variant exists and its parent product exists.
 */
class ProductVariantExists implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Find the product variant
        $variant = ProductVariation::find($value);

        // Validate that the variant exists
        if (!$variant) {
            $fail(__('messages.cart.variant_not_found'));
            return;
        }

        // Find the parent product
        $product = Product::find($variant->product_id);

        // Validate that the product exists
        if (!$product) {
            $fail(__('messages.cart.product_not_found'));
            return;
        }
    }
}

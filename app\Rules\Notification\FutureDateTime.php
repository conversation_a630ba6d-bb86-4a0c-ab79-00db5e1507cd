<?php

namespace App\Rules\Notification;

use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Future Date Time Rule
 *
 * Validates that a datetime is in the future (Tehran timezone).
 */
class FutureDateTime implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        try {
            $tehran = Carbon::parse($value, 'Asia/Tehran');
            if ($tehran->clone()->utc()->lte(now()->utc())) {
                $fail(__('messages.notification.future_time_required'));
            }
        } catch (\Exception) {
            $fail(__('messages.notification.invalid_time_format'));
        }
    }
}

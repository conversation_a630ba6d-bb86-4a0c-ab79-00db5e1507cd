<?php

namespace App\Rules\Address;

use App\Models\User\Address;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Address Ownership Rule
 *
 * Validates that an address exists and belongs to the authenticated user.
 */
class AddressOwnership implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Find the address
        $address = Address::find($value);

        // Check if address exists
        if (!$address) {
            $fail(__('messages.address.not_found'));
            return;
        }

        // Check if the address belongs to the user
        if ($address->user_id !== auth()->id()) {
            $fail(__('messages.address.unauthorized'));
            return;
        }
    }
}

<?php

namespace App\Rules\Product;

use App\Models\Product\Product;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Product Exists By Slug Rule
 *
 * Validates that a product exists with the given slug.
 */
class ProductExistsBySlug implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $product = Product::where('slug', $value)->first();
        
        if (!$product) {
            $fail(__('messages.product.not_found'));
        }
    }
}

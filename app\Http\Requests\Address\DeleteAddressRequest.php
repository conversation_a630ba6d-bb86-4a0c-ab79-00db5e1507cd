<?php

namespace App\Http\Requests\Address;

use App\Rules\Address\AddressOwnership;
use Illuminate\Foundation\Http\FormRequest;

/**
 * Delete Address Request
 *
 * Validates the request data for deleting an address.
 */
class DeleteAddressRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only authenticated users can delete addresses
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'address_id' => [
                'required',
                'string',
                'exists:addresses,_id',
                new AddressOwnership(),
            ],

        ];
    }

    // Custom validation messages are now handled by lang/fa/validation.php

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // Add the address ID from the route parameter
        $this->merge([
            'address_id' => $this->route('id'),
        ]);
    }
}

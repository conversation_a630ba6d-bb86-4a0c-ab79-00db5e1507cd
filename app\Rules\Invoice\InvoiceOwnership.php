<?php

namespace App\Rules\Invoice;

use App\Models\Shopping\Invoice;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Invoice Ownership Rule
 *
 * Validates that an invoice exists and belongs to the authenticated user.
 */
class InvoiceOwnership implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Find the invoice
        $invoice = Invoice::find($value);

        // Check if the invoice belongs to the user
        if ($invoice && $invoice->user_id !== auth()->id()) {
            $fail(__('messages.invoice.not_owned'));
        }
    }
}
